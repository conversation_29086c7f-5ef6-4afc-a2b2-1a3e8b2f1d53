import { tokens } from './tokens';

export interface ThemeConfig {
  mode: 'light' | 'dark';
  tokens: typeof tokens;
}

export const lightTheme: ThemeConfig = {
  mode: 'light',
  tokens,
};

export const darkTheme: ThemeConfig = {
  mode: 'dark',
  tokens,
};

export const defaultTheme = lightTheme;

// Theme utility functions
export const getColor = (theme: ThemeConfig, color: string, shade?: string | number) => {
  const colorParts = color.split('.');
  let colorValue: any = theme.tokens.colors;
  
  for (const part of colorParts) {
    colorValue = colorValue?.[part];
  }
  
  if (shade && typeof colorValue === 'object') {
    return colorValue[shade];
  }
  
  return colorValue;
};

export const getSpacing = (theme: ThemeConfig, spacing: string | number) => {
  return theme.tokens.spacing[spacing as keyof typeof theme.tokens.spacing];
};

export const getFontSize = (theme: ThemeConfig, size: string) => {
  return theme.tokens.typography.fontSize[size as keyof typeof theme.tokens.typography.fontSize];
};

export const getBorderRadius = (theme: ThemeConfig, radius: string) => {
  return theme.tokens.borderRadius[radius as keyof typeof theme.tokens.borderRadius];
};

export const getShadow = (theme: ThemeConfig, shadow: string) => {
  return theme.tokens.shadows[shadow as keyof typeof theme.tokens.shadows];
};

// CSS custom properties generator
export const generateCSSVariables = (theme: ThemeConfig) => {
  const cssVars: Record<string, string> = {};
  
  // Colors
  Object.entries(theme.tokens.colors).forEach(([colorName, colorScale]) => {
    if (typeof colorScale === 'object') {
      Object.entries(colorScale).forEach(([shade, value]) => {
        cssVars[`--color-${colorName}-${shade}`] = value;
      });
    }
  });
  
  // Spacing
  Object.entries(theme.tokens.spacing).forEach(([key, value]) => {
    cssVars[`--spacing-${key}`] = value;
  });
  
  // Typography
  Object.entries(theme.tokens.typography.fontSize).forEach(([key, value]) => {
    const [fontSize, config] = Array.isArray(value) ? value : [value, {}];
    cssVars[`--font-size-${key}`] = fontSize;
    if (config.lineHeight) {
      cssVars[`--line-height-${key}`] = config.lineHeight;
    }
  });
  
  // Border radius
  Object.entries(theme.tokens.borderRadius).forEach(([key, value]) => {
    cssVars[`--border-radius-${key}`] = value;
  });
  
  // Shadows
  Object.entries(theme.tokens.shadows).forEach(([key, value]) => {
    cssVars[`--shadow-${key}`] = value;
  });
  
  return cssVars;
};
