// Main theme exports
export { tokens } from './tokens';
export type {
  Tokens,
  ColorScale,
  ColorShade,
  Spacing,
  FontSize,
  FontWeight,
  BorderRadius,
  Shadow,
  Breakpoint,
} from './tokens';

export {
  lightTheme,
  darkTheme,
  defaultTheme,
  getColor,
  getSpacing,
  getFontSize,
  getBorderRadius,
  getShadow,
  generateCSSVariables,
} from './theme';
export type { ThemeConfig } from './theme';

export {
  ThemeProvider,
  useTheme,
  useTokens,
  useColors,
  useSpacing,
  useTypography,
} from './ThemeProvider';

export {
  cssVar,
  color,
  spacing,
  fontSize,
  lineHeight,
  borderRadius,
  shadow,
  breakpoint,
  themeClass,
  generateTailwindColors,
  buttonStyles,
  cardStyles,
  inputStyles,
  animations,
  delays,
} from './utils';

// Re-export everything as RDSTheme
export * as RDSTheme from './index';
