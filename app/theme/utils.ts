import { tokens } from './tokens';
import type { ColorScale, ColorShade, Spacing, FontSize, BorderRadius, Shadow } from './tokens';

// Utility function to get CSS custom property
export const cssVar = (property: string) => `var(--${property})`;

// Color utilities
export const color = (scale: ColorScale, shade: ColorShade = 500) => 
  cssVar(`color-${scale}-${shade}`);

// Spacing utilities
export const spacing = (size: Spacing) => cssVar(`spacing-${size}`);

// Typography utilities
export const fontSize = (size: FontSize) => cssVar(`font-size-${size}`);
export const lineHeight = (size: FontSize) => cssVar(`line-height-${size}`);

// Border radius utilities
export const borderRadius = (radius: BorderRadius) => cssVar(`border-radius-${radius}`);

// Shadow utilities
export const shadow = (shadowSize: Shadow) => cssVar(`shadow-${shadowSize}`);

// Responsive utilities
export const breakpoint = (bp: keyof typeof tokens.breakpoints) => 
  `@media (min-width: ${tokens.breakpoints[bp]})`;

// Theme-aware class name generator
export const themeClass = (lightClass: string, darkClass: string) => 
  `${lightClass} dark:${darkClass}`;

// Generate Tailwind-compatible color classes
export const generateTailwindColors = () => {
  const colors: Record<string, Record<string, string>> = {};
  
  Object.entries(tokens.colors).forEach(([colorName, colorScale]) => {
    if (typeof colorScale === 'object') {
      colors[colorName] = {};
      Object.entries(colorScale).forEach(([shade, value]) => {
        colors[colorName][shade] = value;
      });
    }
  });
  
  return colors;
};

// Component style utilities
export const buttonStyles = {
  base: `
    inline-flex items-center justify-center
    font-medium transition-colors duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
  `,
  sizes: {
    sm: `px-3 py-1.5 text-sm`,
    md: `px-4 py-2 text-base`,
    lg: `px-6 py-3 text-lg`,
  },
  variants: {
    primary: `
      bg-primary-600 text-white hover:bg-primary-700
      focus:ring-primary-500 dark:bg-primary-500 dark:hover:bg-primary-600
    `,
    secondary: `
      bg-secondary-200 text-secondary-900 hover:bg-secondary-300
      focus:ring-secondary-500 dark:bg-secondary-700 dark:text-secondary-100
      dark:hover:bg-secondary-600
    `,
    outline: `
      border border-secondary-300 text-secondary-700 hover:bg-secondary-50
      focus:ring-secondary-500 dark:border-secondary-600 dark:text-secondary-300
      dark:hover:bg-secondary-800
    `,
  },
};

export const cardStyles = {
  base: `
    bg-white dark:bg-secondary-800
    border border-secondary-200 dark:border-secondary-700
    shadow-sm hover:shadow-md transition-shadow duration-200
  `,
  padding: {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  },
  rounded: {
    sm: 'rounded-md',
    md: 'rounded-lg',
    lg: 'rounded-xl',
  },
};

export const inputStyles = {
  base: `
    block w-full border border-secondary-300 dark:border-secondary-600
    bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100
    placeholder-secondary-500 dark:placeholder-secondary-400
    focus:border-primary-500 focus:ring-primary-500
    disabled:bg-secondary-50 dark:disabled:bg-secondary-900
    disabled:text-secondary-500 dark:disabled:text-secondary-400
  `,
  sizes: {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-base',
    lg: 'px-4 py-3 text-lg',
  },
  rounded: {
    sm: 'rounded-md',
    md: 'rounded-lg',
    lg: 'rounded-xl',
  },
};

// Animation utilities
export const animations = {
  fadeIn: 'animate-fade-in',
  slideUp: 'animate-slide-up',
  slideRight: 'animate-slide-right',
  slideLeft: 'animate-slide-left',
  scale: 'animate-scale',
};

export const delays = {
  100: 'delay-100',
  200: 'delay-200',
  300: 'delay-300',
  400: 'delay-400',
  500: 'delay-500',
};
