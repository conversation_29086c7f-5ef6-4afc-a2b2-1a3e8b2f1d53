import React, { createContext, useContext, useEffect, useState } from 'react';
import { ThemeConfig, lightTheme, darkTheme, generateCSSVariables } from './theme';

interface ThemeContextType {
  theme: ThemeConfig;
  setTheme: (theme: ThemeConfig) => void;
  toggleTheme: () => void;
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeConfig;
}

export function ThemeProvider({ children, defaultTheme = lightTheme }: ThemeProviderProps) {
  const [theme, setTheme] = useState<ThemeConfig>(defaultTheme);

  const toggleTheme = () => {
    setTheme(current => current.mode === 'light' ? darkTheme : lightTheme);
  };

  const isDark = theme.mode === 'dark';

  // Apply CSS variables to document root
  useEffect(() => {
    const cssVars = generateCSSVariables(theme);
    const root = document.documentElement;
    
    Object.entries(cssVars).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
    
    // Apply theme class to body
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme.mode}`);
    
    // Update data attribute for Tailwind dark mode
    if (theme.mode === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      if (!localStorage.getItem('theme-preference')) {
        setTheme(e.matches ? darkTheme : lightTheme);
      }
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Persist theme preference
  useEffect(() => {
    localStorage.setItem('theme-preference', theme.mode);
  }, [theme]);

  // Load saved theme preference on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme-preference');
    if (savedTheme === 'dark') {
      setTheme(darkTheme);
    } else if (savedTheme === 'light') {
      setTheme(lightTheme);
    } else {
      // Use system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setTheme(prefersDark ? darkTheme : lightTheme);
    }
  }, []);

  const value: ThemeContextType = {
    theme,
    setTheme,
    toggleTheme,
    isDark,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook for accessing theme tokens directly
export function useTokens() {
  const { theme } = useTheme();
  return theme.tokens;
}

// Utility hooks for common theme values
export function useColors() {
  const tokens = useTokens();
  return tokens.colors;
}

export function useSpacing() {
  const tokens = useTokens();
  return tokens.spacing;
}

export function useTypography() {
  const tokens = useTokens();
  return tokens.typography;
}
