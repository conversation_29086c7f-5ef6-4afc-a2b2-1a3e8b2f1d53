# RDSTheme Documentation

RDSTheme is a comprehensive design system and theme provider for your React/Remix application. It provides consistent design tokens, theme management, and utility functions.

## Features

- 🎨 **Design Tokens**: Comprehensive color palettes, typography, spacing, and more
- 🌙 **Dark Mode**: Built-in dark mode support with automatic system preference detection
- 🎯 **Type Safety**: Full TypeScript support with type-safe theme access
- 🔧 **Tailwind Integration**: Seamless integration with Tailwind CSS
- 🎭 **Theme Provider**: React context-based theme management
- 🛠️ **Utility Functions**: Helper functions for accessing theme values

## Quick Start

### 1. Basic Usage

The theme is already set up in your application. You can use theme colors directly in your Tailwind classes:

```tsx
// Use theme colors in Tailwind classes
<div className="bg-primary-600 text-white">
  <h1 className="text-secondary-900 dark:text-white">Hello World</h1>
</div>
```

### 2. Using Theme Hooks

```tsx
import { useTheme, useColors, useTokens } from '~/theme';

function MyComponent() {
  const { theme, toggleTheme, isDark } = useTheme();
  const colors = useColors();
  const tokens = useTokens();

  return (
    <div>
      <button onClick={toggleTheme}>
        Switch to {isDark ? 'light' : 'dark'} mode
      </button>
    </div>
  );
}
```

### 3. Available Color Scales

- `primary`: Blue color scale (main brand color)
- `secondary`: Gray color scale (neutral colors)
- `success`: Green color scale
- `warning`: Yellow/Orange color scale
- `error`: Red color scale
- `neutral`: Additional gray scale

Each color scale has shades from 50 (lightest) to 900 (darkest).

### 4. Typography

```tsx
// Font families
className="font-sans" // Inter font
className="font-serif" // Serif fallback
className="font-mono" // Monospace

// Font sizes (with automatic line heights)
className="text-xs text-sm text-base text-lg text-xl text-2xl text-3xl text-4xl text-5xl text-6xl"
```

### 5. Spacing

Use the predefined spacing scale:

```tsx
className="p-4 m-6 gap-8" // Uses theme spacing tokens
```

### 6. Component Styles

Pre-built component styles are available:

```tsx
import { buttonStyles, cardStyles, inputStyles } from '~/theme';

// Use in className
<button className={`${buttonStyles.base} ${buttonStyles.sizes.md} ${buttonStyles.variants.primary}`}>
  Click me
</button>
```

## Theme Structure

### Colors
- Each color has 10 shades (50-900)
- Designed for both light and dark modes
- Accessible contrast ratios

### Typography
- Font families: sans, serif, mono
- Font sizes: xs to 6xl with appropriate line heights
- Font weights: thin to black

### Spacing
- Consistent spacing scale from 0 to 64
- Based on 0.25rem increments

### Border Radius
- From none to full (circle)
- Consistent with modern design trends

### Shadows
- 6 shadow levels: sm, base, md, lg, xl, 2xl
- Subtle and modern appearance

## Advanced Usage

### Custom CSS Variables

The theme automatically generates CSS custom properties:

```css
/* Available CSS variables */
--color-primary-600
--spacing-4
--font-size-lg
--border-radius-md
--shadow-lg
```

### Programmatic Theme Access

```tsx
import { getColor, getSpacing, getFontSize } from '~/theme';

const primaryColor = getColor(theme, 'primary', 600);
const mediumSpacing = getSpacing(theme, '4');
```

### Theme Customization

To customize the theme, modify `app/theme/tokens.ts`:

```tsx
export const tokens = {
  colors: {
    primary: {
      // Your custom primary colors
      500: '#your-color',
      600: '#your-darker-color',
    },
    // ... other customizations
  },
};
```

## Best Practices

1. **Use semantic color names**: Prefer `primary`, `secondary` over specific color names
2. **Consistent spacing**: Use the spacing scale for margins, padding, and gaps
3. **Responsive design**: Leverage the breakpoint tokens for consistent responsive behavior
4. **Dark mode**: Always consider both light and dark mode when using colors
5. **Accessibility**: The theme includes accessible color contrasts by default

## Components

### ThemeToggle

A pre-built component for switching between light and dark modes:

```tsx
import { ThemeToggle } from '~/components/ThemeToggle';

<ThemeToggle />
```

## Migration Guide

If you're migrating from hardcoded colors to RDSTheme:

1. Replace `bg-blue-600` with `bg-primary-600`
2. Replace `text-gray-900` with `text-secondary-900`
3. Replace `border-gray-300` with `border-secondary-300`
4. Add dark mode variants: `dark:bg-secondary-800`

The theme is designed to be a drop-in replacement that enhances your existing Tailwind setup.
