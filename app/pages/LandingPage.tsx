import { useEffect, useRef, useState } from "react";
import { useTheme, useColors } from "~/theme";
import { ThemeToggle } from "~/components/ThemeToggle";

interface Feature {
  title: string;
  description: string;
  icon: JSX.Element;
}

// Custom hook for intersection observer animations
function useIntersectionObserver<T extends HTMLElement = HTMLElement>(options = {}) {
  const elementRef = useRef<T | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsVisible(true);
        // Once the element is visible, we can stop observing it
        if (elementRef.current) {
          observer.unobserve(elementRef.current);
        }
      }
    }, options);

    const currentElement = elementRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [options]);

  return { ref: elementRef, isVisible };
}

// Custom hook for sticky navbar
function useStickyNav() {
  const [isSticky, setIsSticky] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const lastScrollY = useRef(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Make the navbar sticky after scrolling down 100px
      if (currentScrollY > 100) {
        setIsSticky(true);

        // Hide navbar when scrolling down, show when scrolling up
        if (currentScrollY > lastScrollY.current) {
          setIsVisible(false);
        } else {
          setIsVisible(true);
        }
      } else {
        setIsSticky(false);
      }

      lastScrollY.current = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return { isSticky, isVisible };
}

const features: Feature[] = [
  {
    title: "Modern Stack",
    description: "Built with Remix, React, and Tailwind CSS for a modern development experience.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    ),
  },
  {
    title: "Responsive Design",
    description: "Fully responsive design that looks great on all devices, from mobile to desktop.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    ),
  },
  {
    title: "Dark Mode",
    description: "Built-in dark mode support that automatically adapts to user preferences.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
      </svg>
    ),
  },
  {
    title: "TypeScript",
    description: "Full TypeScript support for a type-safe development experience.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
      </svg>
    ),
  },
  {
    title: "Fast Performance",
    description: "Optimized for performance with server-side rendering and client-side hydration.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
  },
  {
    title: "Developer Experience",
    description: "Great developer experience with hot module replacement and fast refresh.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
];

export default function LandingPage() {
  const { isSticky, isVisible } = useStickyNav();
  const { theme } = useTheme();
  const colors = useColors();
  const hero = useIntersectionObserver<HTMLElement>({ threshold: 0.75 });
  const heroText = useIntersectionObserver<HTMLHeadingElement>({ threshold: 0.75 });
  const heroButtons = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-100 dark:from-gray-900 dark:to-gray-950">
      {/* Navigation */}
      <nav className={`container mx-auto px-6 py-4 transition-all duration-300 ${
        isSticky
          ? `navbar-fixed ${isVisible ? '' : 'navbar-hidden'}`
          : ''
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="h-10 w-10">
              <img
                src="/logo-light.png"
                alt="Logo"
                className="block h-full w-auto dark:hidden"
              />
              <img
                src="/logo-dark.png"
                alt="Logo"
                className="hidden h-full w-auto dark:block"
              />
            </div>
            <span className="ml-3 text-xl font-bold text-gray-800 dark:text-white">AppName</span>
          </div>
          <div className="hidden md:block">
            <div className="flex items-center space-x-8">
              <a href="#features" className="text-secondary-600 hover:text-secondary-900 dark:text-secondary-300 dark:hover:text-white">Features</a>
              <a href="#testimonials" className="text-secondary-600 hover:text-secondary-900 dark:text-secondary-300 dark:hover:text-white">Testimonials</a>
              <a href="#contact" className="text-secondary-600 hover:text-secondary-900 dark:text-secondary-300 dark:hover:text-white">Contact</a>
              <ThemeToggle />
              <a href="#" className="rounded-md bg-primary-600 px-4 py-2 text-white hover:bg-primary-700">Get Started</a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section ref={hero.ref} className="container mx-auto px-6 py-16 text-center md:py-24">
        <h1
          ref={heroText.ref}
          className={`mb-6 text-4xl font-extrabold leading-tight text-secondary-900 dark:text-white md:text-5xl lg:text-6xl animate-fade-in ${heroText.isVisible ? 'visible' : ''}`}
        >
          Build Amazing Web Applications <span className="text-primary-600">Faster</span>
        </h1>
        <p
          className={`mx-auto mb-10 max-w-2xl text-lg text-secondary-600 dark:text-secondary-300 animate-slide-up delay-200 ${heroText.isVisible ? 'visible' : ''}`}
        >
          A modern, responsive boilerplate with everything you need to start your next web project. Built with Remix, React, and Tailwind CSS.
        </p>
        <div
          ref={heroButtons.ref}
          className={`flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0 animate-slide-up delay-400 ${heroButtons.isVisible ? 'visible' : ''}`}
        >
          <a href="#" className="rounded-md bg-primary-600 px-8 py-3 text-lg font-medium text-white hover:bg-primary-700 transition-transform hover:scale-105">Get Started</a>
          <a href="#" className="rounded-md border border-secondary-300 bg-white px-8 py-3 text-lg font-medium text-secondary-700 hover:bg-secondary-50 dark:border-secondary-700 dark:bg-secondary-800 dark:text-secondary-200 dark:hover:bg-secondary-700 transition-transform hover:scale-105">Learn More</a>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="bg-white py-16 dark:bg-secondary-800 md:py-24">
        <div className="container mx-auto px-6">
          {(() => {
            const featuresTitle = useIntersectionObserver<HTMLHeadingElement>({ threshold: 0.75 });
            const featuresGrid = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });

            return (
              <>
                <h2
                  ref={featuresTitle.ref}
                  className={`mb-12 text-center text-3xl font-bold text-secondary-900 dark:text-white md:text-4xl animate-slide-up ${featuresTitle.isVisible ? 'visible' : ''}`}
                >
                  Key Features
                </h2>
                <div
                  ref={featuresGrid.ref}
                  className={`grid gap-8 md:grid-cols-2 lg:grid-cols-3 ${featuresGrid.isVisible ? 'visible' : ''}`}
                >
                  {features.map((feature, index) => {
                    const featureCard = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });
                    return (
                      <div
                        key={feature.title}
                        ref={featureCard.ref}
                        className={`rounded-lg border border-secondary-200 bg-white p-6 shadow-sm dark:border-secondary-700 dark:bg-secondary-900 animate-scale delay-${index * 100} ${featureCard.isVisible ? 'visible' : ''}`}
                      >
                        <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-full bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-300">
                          {feature.icon}
                        </div>
                        <h3 className="mb-2 text-xl font-bold text-secondary-900 dark:text-white">{feature.title}</h3>
                        <p className="text-secondary-600 dark:text-secondary-400">{feature.description}</p>
                      </div>
                    );
                  })}
                </div>
              </>
            );
          })()}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary-600 py-16 dark:bg-primary-800 md:py-24">
        <div className="container mx-auto px-6 text-center">
          {(() => {
            const ctaTitle = useIntersectionObserver<HTMLHeadingElement>({ threshold: 0.75 });
            const ctaText = useIntersectionObserver<HTMLParagraphElement>({ threshold: 0.75 });
            const ctaButton = useIntersectionObserver<HTMLAnchorElement>({ threshold: 0.75 });

            return (
              <>
                <h2
                  ref={ctaTitle.ref}
                  className={`mb-6 text-3xl font-bold text-white md:text-4xl animate-slide-up ${ctaTitle.isVisible ? 'visible' : ''}`}
                >
                  Ready to get started?
                </h2>
                <p
                  ref={ctaText.ref}
                  className={`mx-auto mb-8 max-w-2xl text-lg text-primary-100 animate-slide-up delay-200 ${ctaText.isVisible ? 'visible' : ''}`}
                >
                  Join thousands of developers who are already building amazing applications with our boilerplate.
                </p>
                <a
                  ref={ctaButton.ref}
                  href="#"
                  className={`inline-block rounded-md bg-white px-8 py-3 text-lg font-medium text-primary-600 hover:bg-secondary-100 transition-transform hover:scale-105 animate-slide-up delay-400 ${ctaButton.isVisible ? 'visible' : ''}`}
                >
                  Start Building Now
                </a>
              </>
            );
          })()}
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-secondary-100 py-12 dark:bg-secondary-900">
        <div className="container mx-auto px-6">
          {(() => {
            const footerContent = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });
            const footerCopyright = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });

            return (
              <>
                <div
                  ref={footerContent.ref}
                  className={`flex flex-col items-center justify-between md:flex-row animate-fade-in ${footerContent.isVisible ? 'visible' : ''}`}
                >
                  <div className="mb-6 flex items-center md:mb-0">
                    <div className="h-8 w-8">
                      <img
                        src="/logo-light.png"
                        alt="Logo"
                        className="block h-full w-auto dark:hidden"
                      />
                      <img
                        src="/logo-dark.png"
                        alt="Logo"
                        className="hidden h-full w-auto dark:block"
                      />
                    </div>
                    <span className="ml-2 text-lg font-bold text-secondary-800 dark:text-white">AppName</span>
                  </div>
                  <div className="flex space-x-6">
                    <a href="#" className="text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-white transition-transform hover:scale-110">
                      <span className="sr-only">GitHub</span>
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                      </svg>
                    </a>
                    <a href="#" className="text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-white transition-transform hover:scale-110">
                      <span className="sr-only">Twitter</span>
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                      </svg>
                    </a>
                  </div>
                </div>
                <div
                  ref={footerCopyright.ref}
                  className={`mt-8 border-t border-secondary-200 pt-8 text-center dark:border-secondary-700 animate-fade-in delay-300 ${footerCopyright.isVisible ? 'visible' : ''}`}
                >
                  <p className="text-secondary-600 dark:text-secondary-400">© 2023 AppName. All rights reserved.</p>
                </div>
              </>
            );
          })()}
        </div>
      </footer>
    </div>
  );
}