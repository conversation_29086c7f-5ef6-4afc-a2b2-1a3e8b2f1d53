@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  @apply bg-white dark:bg-gray-950;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

/* Animation classes */
.animate-fade-in {
  opacity: 0;
  transition: opacity 0.6s ease-in-out, transform 0.6s ease-in-out;
}

.animate-fade-in.visible {
  opacity: 1;
}

.animate-slide-up {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-slide-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.animate-slide-right {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-slide-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.animate-slide-left {
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-slide-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.animate-scale {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-scale.visible {
  opacity: 1;
  transform: scale(1);
}

/* Delay utilities */
.delay-100 {
  transition-delay: 100ms;
}

.delay-200 {
  transition-delay: 200ms;
}

.delay-300 {
  transition-delay: 300ms;
}

.delay-400 {
  transition-delay: 400ms;
}

.delay-500 {
  transition-delay: 500ms;
}

/* Sticky navbar on scroll */
.navbar-fixed {
  @apply fixed top-0 left-0 right-0 z-50 bg-white/90 shadow-md backdrop-blur-sm dark:bg-gray-900/90;
  transition: transform 0.3s ease;
}

.navbar-hidden {
  transform: translateY(-100%);
}
