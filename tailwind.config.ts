import type { Config } from "tailwindcss";
import { tokens } from "./app/theme/tokens";

// Generate Tailwind-compatible colors from theme tokens
const generateColors = () => {
  const colors: Record<string, Record<string, string>> = {};

  Object.entries(tokens.colors).forEach(([colorName, colorScale]) => {
    if (typeof colorScale === 'object') {
      colors[colorName] = {};
      Object.entries(colorScale).forEach(([shade, value]) => {
        colors[colorName][shade] = value;
      });
    }
  });

  return colors;
};

export default {
  content: ["./app/**/{**,.client,.server}/**/*.{js,jsx,ts,tsx}"],
  darkMode: 'class',
  theme: {
    extend: {
      colors: generateColors(),
      fontFamily: tokens.typography.fontFamily,
      fontSize: tokens.typography.fontSize,
      fontWeight: tokens.typography.fontWeight,
      spacing: tokens.spacing,
      borderRadius: tokens.borderRadius,
      boxShadow: tokens.shadows,
      screens: tokens.breakpoints,
    },
  },
  plugins: [],
} satisfies Config;
